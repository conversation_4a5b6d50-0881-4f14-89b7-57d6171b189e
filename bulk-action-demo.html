<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Bulk Action Bar Demo</title>
    
    <!-- Material Icons -->
    <link href="https://fonts.googleapis.com/icon?family=Material+Icons" rel="stylesheet">
    
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Roboto:wght@300;400;500;700&display=swap" rel="stylesheet">
    
    <!-- Alpine.js -->
    <script defer src="https://unpkg.com/alpinejs@3.x.x/dist/cdn.min.js"></script>
    
    <!-- Custom CSS -->
    <link rel="stylesheet" href="styles.css">
    
    <style>
        /* Demo specific styles */
        .demo-container {
            max-width: 800px;
            margin: 0 auto;
            padding: 40px 20px;
            font-family: 'Roboto', sans-serif;
        }
        
        .demo-title {
            font-size: 32px;
            font-weight: 500;
            color: var(--text-primary);
            margin-bottom: 16px;
            text-align: center;
        }
        
        .demo-description {
            font-size: 16px;
            color: var(--text-secondary);
            margin-bottom: 40px;
            text-align: center;
            line-height: 1.5;
        }
        
        .demo-controls {
            display: flex;
            gap: 16px;
            justify-content: center;
            margin-bottom: 40px;
            flex-wrap: wrap;
        }
        
        .demo-btn {
            padding: 12px 24px;
            border: 1px solid var(--border-medium);
            border-radius: 8px;
            background: var(--background-surface);
            color: var(--text-primary);
            cursor: pointer;
            transition: all 0.2s ease;
            font-size: 14px;
            font-weight: 500;
        }
        
        .demo-btn:hover {
            background: var(--surface-hover);
            border-color: var(--primary-color);
        }
        
        .demo-btn.active {
            background: var(--primary-color);
            color: white;
            border-color: var(--primary-color);
        }
        
        .demo-info {
            background: var(--background-secondary);
            padding: 24px;
            border-radius: 12px;
            margin-bottom: 40px;
        }
        
        .demo-info h3 {
            font-size: 18px;
            font-weight: 500;
            color: var(--text-primary);
            margin-bottom: 12px;
        }
        
        .demo-info ul {
            list-style: none;
            padding: 0;
        }
        
        .demo-info li {
            font-size: 14px;
            color: var(--text-secondary);
            margin-bottom: 8px;
            padding-left: 20px;
            position: relative;
        }
        
        .demo-info li::before {
            content: '•';
            color: var(--primary-color);
            position: absolute;
            left: 0;
            font-weight: bold;
        }
        
        .theme-toggle {
            position: fixed;
            top: 20px;
            right: 20px;
            background: var(--primary-color);
            color: white;
            border: none;
            border-radius: 50%;
            width: 48px;
            height: 48px;
            cursor: pointer;
            box-shadow: 0 4px 12px var(--shadow-medium);
            transition: all 0.2s ease;
        }
        
        .theme-toggle:hover {
            transform: scale(1.1);
        }
    </style>
</head>
<body x-data="bulkActionDemo()">
    <!-- Theme Toggle -->
    <button class="theme-toggle" @click="toggleTheme()" :title="isDark ? 'Switch to Light Mode' : 'Switch to Dark Mode'">
        <span class="material-icons" x-text="isDark ? 'light_mode' : 'dark_mode'"></span>
    </button>

    <div class="demo-container">
        <h1 class="demo-title">Bulk Action Bar Component</h1>
        <p class="demo-description">
            A minimal and modern bulk action bar with rounded white container, slight shadow, and proper spacing between buttons.
            Features Material Icons for archive and delete actions with hover effects.
        </p>
        
        <div class="demo-info">
            <h3>Features:</h3>
            <ul>
                <li>Rounded white container with subtle shadow</li>
                <li>Three sections: selected count, archive button, delete button</li>
                <li>Material Icons for better consistency</li>
                <li>Smooth hover effects and transitions</li>
                <li>Responsive design for mobile and desktop</li>
                <li>Dark mode support</li>
                <li>Proper visual separation between elements</li>
            </ul>
        </div>
        
        <div class="demo-controls">
            <button class="demo-btn" :class="{ 'active': selectedCount === 0 }" @click="setSelectedCount(0)">
                No Selection
            </button>
            <button class="demo-btn" :class="{ 'active': selectedCount === 1 }" @click="setSelectedCount(1)">
                1 Selected
            </button>
            <button class="demo-btn" :class="{ 'active': selectedCount === 3 }" @click="setSelectedCount(3)">
                3 Selected
            </button>
            <button class="demo-btn" :class="{ 'active': selectedCount === 10 }" @click="setSelectedCount(10)">
                10 Selected
            </button>
        </div>
    </div>

    <!-- Bulk Action Bar -->
    <div class="bulk-action-bar" :class="{ 'visible': selectedCount > 0 }">
        <div class="bulk-selected-count" x-text="selectedCount === 1 ? '1 selected' : `${selectedCount} selected`"></div>
        <div class="bulk-actions">
            <button class="bulk-action-btn archive" @click="handleArchive()">
                <span class="material-icons">archive</span>
                <span>Archive</span>
            </button>
            <button class="bulk-action-btn delete" @click="handleDelete()">
                <span class="material-icons">delete</span>
                <span>Delete</span>
            </button>
        </div>
    </div>

    <script>
        function bulkActionDemo() {
            return {
                selectedCount: 0,
                isDark: false,
                
                setSelectedCount(count) {
                    this.selectedCount = count;
                },
                
                handleArchive() {
                    alert(`Archive ${this.selectedCount} item(s)`);
                },
                
                handleDelete() {
                    if (confirm(`Are you sure you want to delete ${this.selectedCount} item(s)?`)) {
                        alert(`Deleted ${this.selectedCount} item(s)`);
                        this.selectedCount = 0;
                    }
                },
                
                toggleTheme() {
                    this.isDark = !this.isDark;
                    document.documentElement.setAttribute('data-theme', this.isDark ? 'dark' : 'light');
                }
            }
        }
    </script>
</body>
</html>
