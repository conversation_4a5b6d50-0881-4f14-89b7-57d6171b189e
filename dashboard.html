<!-- Dashboard Overview -->
<div class="dashboard-container">
    <!-- Quick Stats Cards -->
    <div class="stats-grid">
        <div class="stat-card-column">
            <div class="stat-card-header">
                <div class="stat-icon">
                    <span class="material-icons">groups_2</span>
                </div>
                <div class="stat-header-content">
                    <div class="stat-label-main">Élèves inscrits</div>
                    <div class="stat-change positive">+12 ce mois</div>
                </div>
            </div>
            <div class="stat-grid-content">
                <div class="stat-grid-item">
                    <div class="stat-number-small">672</div>
                    <div class="stat-label-small">Garçons</div>
                </div>
                <div class="stat-grid-item">
                    <div class="stat-number-small">575</div>
                    <div class="stat-label-small">Filles</div>
                </div>
                <div class="stat-grid-item stat-grid-total">
                    <div class="stat-number-small">1,247</div>
                    <div class="stat-label-small">Total</div>
                </div>
            </div>
        </div>

        <div class="stat-card">
            <div class="stat-icon">
                <span class="material-icons">class</span>
            </div>
            <div class="stat-content">
                <div class="stat-number">42</div>
                <div class="stat-label">Classes actives</div>
                <div class="stat-change neutral">Stable</div>
            </div>
        </div>

        <div class="stat-card">
            <div class="stat-icon">
                <span class="material-icons">people</span>
            </div>
            <div class="stat-content">
                <div class="stat-number">87</div>
                <div class="stat-label">Enseignants</div>
                <div class="stat-change positive">+3 ce mois</div>
            </div>
        </div>

        <div class="stat-card">
            <div class="stat-icon">
                <span class="material-icons">account_balance_wallet</span>
            </div>
            <div class="stat-content">
                <div class="stat-number">€245,680</div>
                <div class="stat-label">Revenus ce mois</div>
                <div class="stat-change positive">+8.5%</div>
            </div>
        </div>
    </div>

    <!-- Charts and Analytics Section -->
    <div class="dashboard-grid">
        <!-- Student Enrollment Chart -->
        <div class="dashboard-card">
            <div class="card-header">
                <h3>Évolution des inscriptions</h3>
                <div class="card-actions">
                    <span class="material-icons">more_vert</span>
                </div>
            </div>
            <div class="card-content">
                <canvas id="enrollmentChart" width="400" height="200"></canvas>
            </div>
        </div>

        <!-- Payment Status Overview -->
        <div class="dashboard-card">
            <div class="card-header">
                <h3>État des paiements</h3>
                <div class="card-actions">
                    <span class="material-icons">more_vert</span>
                </div>
            </div>
            <div class="card-content">
                <canvas id="paymentChart" width="400" height="200"></canvas>
            </div>
        </div>

        <!-- Grade Distribution -->
        <div class="dashboard-card">
            <div class="card-header">
                <h3>Répartition des notes</h3>
                <div class="card-actions">
                    <span class="material-icons">more_vert</span>
                </div>
            </div>
            <div class="card-content">
                <canvas id="gradesChart" width="400" height="200"></canvas>
            </div>
        </div>

        <!-- Attendance Overview -->
        <div class="dashboard-card">
            <div class="card-header">
                <h3>Taux de présence</h3>
                <div class="card-actions">
                    <span class="material-icons">more_vert</span>
                </div>
            </div>
            <div class="card-content">
                <div class="attendance-stats">
                    <div class="attendance-item">
                        <div class="attendance-circle present">
                            <span>94%</span>
                        </div>
                        <div class="attendance-label">Présents</div>
                    </div>
                    <div class="attendance-item">
                        <div class="attendance-circle absent">
                            <span>4%</span>
                        </div>
                        <div class="attendance-label">Absents</div>
                    </div>
                    <div class="attendance-item">
                        <div class="attendance-circle late">
                            <span>2%</span>
                        </div>
                        <div class="attendance-label">Retards</div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Recent Activities and Quick Actions -->
    <div class="dashboard-grid-2">
        <!-- Recent Activities -->
        <div class="dashboard-card">
            <div class="card-header">
                <h3>Activités récentes</h3>
                <div class="card-actions">
                    <span class="material-icons">refresh</span>
                </div>
            </div>
            <div class="card-content">
                <div class="activity-list">
                    <div class="activity-item">
                        <div class="activity-icon">
                            <span class="material-icons">person_add</span>
                        </div>
                        <div class="activity-content">
                            <div class="activity-title">Nouvel élève inscrit</div>
                            <div class="activity-subtitle">Ahmed Ben Ali - 6ème A</div>
                            <div class="activity-time">Il y a 2 heures</div>
                        </div>
                    </div>
                    <div class="activity-item">
                        <div class="activity-icon">
                            <span class="material-icons">payment</span>
                        </div>
                        <div class="activity-content">
                            <div class="activity-title">Paiement reçu</div>
                            <div class="activity-subtitle">Fatima Zahra - Frais de scolarité</div>
                            <div class="activity-time">Il y a 4 heures</div>
                        </div>
                    </div>
                    <div class="activity-item">
                        <div class="activity-icon">
                            <span class="material-icons">assignment</span>
                        </div>
                        <div class="activity-content">
                            <div class="activity-title">Nouveau bulletin généré</div>
                            <div class="activity-subtitle">5ème B - Trimestre 1</div>
                            <div class="activity-time">Il y a 6 heures</div>
                        </div>
                    </div>
                    <div class="activity-item">
                        <div class="activity-icon">
                            <span class="material-icons">event_busy</span>
                        </div>
                        <div class="activity-content">
                            <div class="activity-title">Absence signalée</div>
                            <div class="activity-subtitle">Mohamed Alami - Maladie</div>
                            <div class="activity-time">Il y a 8 heures</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Quick Actions -->
        <div class="dashboard-card">
            <div class="card-header">
                <h3>Actions rapides</h3>
            </div>
            <div class="card-content">
                <div class="quick-actions-grid">
                    <div class="quick-action-item">
                        <div class="quick-action-icon">
                            <span class="material-icons">person_add</span>
                        </div>
                        <div class="quick-action-label">Ajouter un élève</div>
                    </div>
                    <div class="quick-action-item">
                        <div class="quick-action-icon">
                            <span class="material-icons">payment</span>
                        </div>
                        <div class="quick-action-label">Enregistrer un paiement</div>
                    </div>
                    <div class="quick-action-item">
                        <div class="quick-action-icon">
                            <span class="material-icons">assignment</span>
                        </div>
                        <div class="quick-action-label">Générer un bulletin</div>
                    </div>
                    <div class="quick-action-item">
                        <div class="quick-action-icon">
                            <span class="material-icons">list_alt</span>
                        </div>
                        <div class="quick-action-label">Liste de classe</div>
                    </div>
                    <div class="quick-action-item">
                        <div class="quick-action-icon">
                            <span class="material-icons">event_busy</span>
                        </div>
                        <div class="quick-action-label">Marquer une absence</div>
                    </div>
                    <div class="quick-action-item">
                        <div class="quick-action-icon">
                            <span class="material-icons">assessment</span>
                        </div>
                        <div class="quick-action-label">Rapport mensuel</div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Class Performance Overview -->
    <div class="dashboard-card full-width">
        <div class="card-header">
            <h3>Performance par classe</h3>
            <div class="card-actions">
                <select class="mdc-select__native-control">
                    <option value="trimestre1">Trimestre 1</option>
                    <option value="trimestre2">Trimestre 2</option>
                    <option value="trimestre3">Trimestre 3</option>
                </select>
            </div>
        </div>
        <div class="card-content">
            <div class="performance-table">
                <table class="mdc-data-table__table">
                    <thead>
                        <tr class="mdc-data-table__header-row">
                            <th class="mdc-data-table__header-cell">Classe</th>
                            <th class="mdc-data-table__header-cell">Effectif</th>
                            <th class="mdc-data-table__header-cell">Moyenne générale</th>
                            <th class="mdc-data-table__header-cell">Taux de réussite</th>
                            <th class="mdc-data-table__header-cell">Présence</th>
                        </tr>
                    </thead>
                    <tbody class="mdc-data-table__content">
                        <tr class="mdc-data-table__row">
                            <td class="mdc-data-table__cell">6ème A</td>
                            <td class="mdc-data-table__cell">28</td>
                            <td class="mdc-data-table__cell">14.2/20</td>
                            <td class="mdc-data-table__cell">85%</td>
                            <td class="mdc-data-table__cell">96%</td>
                        </tr>
                        <tr class="mdc-data-table__row">
                            <td class="mdc-data-table__cell">6ème B</td>
                            <td class="mdc-data-table__cell">30</td>
                            <td class="mdc-data-table__cell">13.8/20</td>
                            <td class="mdc-data-table__cell">82%</td>
                            <td class="mdc-data-table__cell">94%</td>
                        </tr>
                        <tr class="mdc-data-table__row">
                            <td class="mdc-data-table__cell">5ème A</td>
                            <td class="mdc-data-table__cell">26</td>
                            <td class="mdc-data-table__cell">15.1/20</td>
                            <td class="mdc-data-table__cell">88%</td>
                            <td class="mdc-data-table__cell">97%</td>
                        </tr>
                        <tr class="mdc-data-table__row">
                            <td class="mdc-data-table__cell">5ème B</td>
                            <td class="mdc-data-table__cell">29</td>
                            <td class="mdc-data-table__cell">14.5/20</td>
                            <td class="mdc-data-table__cell">86%</td>
                            <td class="mdc-data-table__cell">95%</td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</div>

<!-- Chart.js for charts -->
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
// Initialize charts when dashboard loads
document.addEventListener('DOMContentLoaded', function() {
    initializeDashboardCharts();
});

function initializeDashboardCharts() {
    // Enrollment Chart
    const enrollmentCtx = document.getElementById('enrollmentChart');
    if (enrollmentCtx) {
        new Chart(enrollmentCtx, {
            type: 'line',
            data: {
                labels: ['Jan', 'Fév', 'Mar', 'Avr', 'Mai', 'Jun'],
                datasets: [{
                    label: 'Inscriptions',
                    data: [1180, 1195, 1210, 1225, 1235, 1247],
                    borderColor: 'var(--primary-color)',
                    backgroundColor: 'var(--primary-color-light)',
                    tension: 0.4
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        display: false
                    }
                }
            }
        });
    }

    // Payment Chart
    const paymentCtx = document.getElementById('paymentChart');
    if (paymentCtx) {
        new Chart(paymentCtx, {
            type: 'doughnut',
            data: {
                labels: ['Payé', 'En retard', 'Non payé'],
                datasets: [{
                    data: [75, 15, 10],
                    backgroundColor: [
                        'var(--success-color)',
                        'var(--warning-color)',
                        'var(--error-color)'
                    ]
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false
            }
        });
    }

    // Grades Chart
    const gradesCtx = document.getElementById('gradesChart');
    if (gradesCtx) {
        new Chart(gradesCtx, {
            type: 'bar',
            data: {
                labels: ['0-5', '6-10', '11-15', '16-20'],
                datasets: [{
                    label: 'Nombre d\'élèves',
                    data: [45, 320, 680, 202],
                    backgroundColor: 'var(--primary-color-light)'
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        display: false
                    }
                }
            }
        });
    }
}
</script>
